# WordPress Migration Plan: 4.7.2 Apache → Latest Nginx

## Current Situation Analysis

### What You're Dealing With

- **WordPress 4.7.2** (Released Jan 2017 - 8 years old!)
- **Apache server** (legacy configuration)
- **No CDN** (vulnerable to scraping/DDoS)
- **No custom page builders** (good for migration)
- **Custom plugins and themes** (potential compatibility issues)

### Critical Security Concerns

WordPress 4.7.2 has **numerous critical vulnerabilities**:

- SQL injection vulnerabilities
- XSS vulnerabilities
- Remote code execution risks
- Missing security patches from 8 years of updates

## Migration Implications & Key Questions

### 1. Pre-Migration Assessment Questions

- **Plugin Inventory**: What custom plugins are installed? Are they still maintained?
- **Theme Analysis**: Is the theme custom-built or third-party? Does it use deprecated functions?
- **Content Volume**: How large is the database? How many posts/pages/media files?
- **Traffic Patterns**: What's the current traffic volume and peak usage times?
- **Custom Code**: Any custom PHP code in themes/plugins that might break?
- **Integrations**: External services, APIs, or third-party tools connected?

### 2. Apache vs Nginx Considerations

#### Apache (Current)

- **Role**: Web server handling HTTP requests, PHP processing, and file serving
- **Configuration**: Uses .htaccess files for URL rewriting and redirects
- **Resource Usage**: Higher memory consumption, process-based model

#### Nginx (Target)

- **Benefits**:
  - Much better performance and lower resource usage
  - Better handling of concurrent connections
  - Built-in load balancing and caching capabilities
  - More efficient static file serving
- **Challenges**:
  - No .htaccess support - need to convert rules to Nginx format
  - Different configuration syntax
  - Requires PHP-FPM for PHP processing

### 3. WordPress Version Jump Implications

#### Major Changes from 4.7.2 → 6.4+ (Latest)

- **Block Editor (Gutenberg)**: Completely new editor (can be disabled)
- **PHP Requirements**: Minimum PHP 7.4+ (likely need PHP 8.0+)
- **Database Changes**: Multiple schema updates
- **Theme/Plugin Compatibility**: Many older plugins may break
- **Security Improvements**: Modern authentication, better sanitization

## Migration Strategy

### Phase 1: Assessment & Preparation (1-2 weeks)

1. **Complete Site Audit**

   - Document all plugins, themes, and customizations
   - Check plugin/theme compatibility with latest WordPress
   - Identify deprecated functions and security issues

2. **Backup Everything**

   - Full file system backup
   - Complete database export
   - Document current Apache configuration

3. **Compatibility Testing**
   - Test plugins/themes on staging with latest WordPress
   - Identify what needs updating or replacement

### Phase 2: Environment Setup (1 week)

1. **New Server Setup**

   - Install Nginx + PHP-FPM + MySQL/MariaDB
   - Configure SSL certificates
   - Set up proper file permissions

2. **WordPress Installation**
   - Fresh WordPress installation
   - Configure wp-config.php with security keys
   - Set up proper directory structure

### Phase 3: Migration & Testing (2-3 weeks)

1. **Content Migration**

   - Database migration with URL updates
   - File migration (wp-content directory)
   - Convert .htaccess rules to Nginx format

2. **Functionality Testing**
   - Test all pages and functionality
   - Verify forms, search, and user registration
   - Check mobile responsiveness

### Phase 4: Performance & Security (1 week)

1. **CDN Implementation**

   - CloudFlare or similar for DDoS protection
   - Static asset caching and optimization

2. **Bot Protection**

   - Rate limiting configuration
   - Bot detection and blocking
   - Implement fail2ban or similar

3. **Performance Optimization**
   - Caching plugins (WP Rocket, W3 Total Cache)
   - Database optimization
   - Image optimization

### Phase 5: Go-Live & Monitoring (1 week)

1. **DNS Cutover**

   - Update DNS records
   - Monitor for issues
   - Have rollback plan ready

2. **Post-Migration**
   - Performance monitoring
   - Security scanning
   - User feedback collection

## Risk Assessment

### High Risk Items

- **Plugin Compatibility**: 8-year-old plugins likely incompatible
- **Custom Theme Code**: May use deprecated WordPress functions
- **Database Migration**: Large databases may have timeout issues
- **SEO Impact**: URL structure changes could affect rankings

### Mitigation Strategies

- Thorough staging environment testing
- Gradual migration approach
- 301 redirects for any URL changes
- Comprehensive backups and rollback plan

## Timeline Estimate

**Total Duration**: 6-8 weeks

- Assessment: 1-2 weeks
- Setup: 1 week
- Migration/Testing: 2-3 weeks
- Optimization: 1 week
- Go-live/Monitoring: 1 week

## Budget Considerations

- New server hosting costs
- CDN service fees
- Potential plugin/theme replacement costs
- Development time for custom code updates
- SSL certificate costs (if not using Let's Encrypt)

## Success Metrics

- Site performance improvement (page load times)
- Reduced server crashes from bot traffic
- Improved security posture
- Maintained or improved SEO rankings
- User experience preservation

## Detailed Technical Specifications

### Nginx Configuration Requirements

#### Basic Nginx Virtual Host

```nginx
server {
    listen 80;
    listen [::]:80;
    server_name example.com www.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name example.com www.example.com;

    root /var/www/html;
    index index.php index.html index.htm;

    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # WordPress specific rules
    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Bot Protection & Rate Limiting
    location = /xmlrpc.php {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~* /(?:uploads|files)/.*\.php$ {
        deny all;
    }

    # Static file caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### Rate Limiting Configuration

```nginx
# In http block
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;

# In server block
location /wp-login.php {
    limit_req zone=login burst=2 nodelay;
    include snippets/fastcgi-php.conf;
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
}

location /wp-admin/ {
    limit_req zone=general burst=5 nodelay;
    try_files $uri $uri/ /index.php?$args;
}
```

### PHP-FPM Configuration

#### Recommended PHP 8.1+ Settings

```ini
# /etc/php/8.1/fpm/pool.d/www.conf
[www]
user = www-data
group = www-data
listen = /var/run/php/php8.1-fpm.sock
listen.owner = www-data
listen.group = www-data
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.process_idle_timeout = 10s
pm.max_requests = 500

# /etc/php/8.1/fpm/php.ini
memory_limit = 256M
upload_max_filesize = 64M
post_max_size = 64M
max_execution_time = 300
max_input_vars = 3000
```

### Database Migration Script

#### WordPress Database Update Process

```bash
#!/bin/bash
# migrate-database.sh

# 1. Export old database
mysqldump -u old_user -p old_database > wordpress_backup.sql

# 2. Create new database
mysql -u root -p -e "CREATE DATABASE new_wordpress_db;"
mysql -u root -p -e "GRANT ALL PRIVILEGES ON new_wordpress_db.* TO 'wp_user'@'localhost' IDENTIFIED BY 'secure_password';"

# 3. Import and update URLs
mysql -u wp_user -p new_wordpress_db < wordpress_backup.sql

# 4. Update URLs in database
mysql -u wp_user -p new_wordpress_db -e "
UPDATE wp_options SET option_value = replace(option_value, 'http://old-domain.com', 'https://new-domain.com');
UPDATE wp_posts SET post_content = replace(post_content, 'http://old-domain.com', 'https://new-domain.com');
UPDATE wp_postmeta SET meta_value = replace(meta_value, 'http://old-domain.com', 'https://new-domain.com');
UPDATE wp_comments SET comment_content = replace(comment_content, 'http://old-domain.com', 'https://new-domain.com');
"
```

### .htaccess to Nginx Conversion

#### Common WordPress .htaccess Rules → Nginx

```apache
# Original .htaccess
RewriteEngine On
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]

# Custom redirects
Redirect 301 /old-page/ /new-page/
RedirectMatch 301 ^/category/(.*)$ /blog/$1
```

```nginx
# Nginx equivalent
location / {
    try_files $uri $uri/ /index.php?$args;
}

# Custom redirects
location = /old-page/ {
    return 301 /new-page/;
}

location ~ ^/category/(.*)$ {
    return 301 /blog/$1;
}
```

### CDN Implementation Strategy

#### CloudFlare Configuration

1. **DNS Setup**

   - Point A record to new server IP
   - Enable CloudFlare proxy (orange cloud)
   - Set up CNAME for www subdomain

2. **Security Rules**

   ```
   # Block known bad bots
   (http.user_agent contains "SemrushBot") or
   (http.user_agent contains "AhrefsBot") or
   (http.user_agent contains "MJ12bot")
   Action: Block

   # Rate limiting for wp-admin
   (http.request.uri.path contains "/wp-admin/") and
   (rate(1m) > 30)
   Action: Challenge
   ```

3. **Page Rules**

   ```
   *example.com/wp-admin/*
   - Security Level: High
   - Cache Level: Bypass

   *example.com/wp-content/uploads/*
   - Cache Level: Cache Everything
   - Edge Cache TTL: 1 month
   ```

### WordPress Security Hardening

#### wp-config.php Security Enhancements

```php
// Security keys (generate new ones)
define('AUTH_KEY',         'put your unique phrase here');
define('SECURE_AUTH_KEY',  'put your unique phrase here');
// ... (generate all 8 keys)

// Database settings
define('DB_NAME', 'database_name');
define('DB_USER', 'username');
define('DB_PASSWORD', 'password');
define('DB_HOST', 'localhost');

// Security enhancements
define('DISALLOW_FILE_EDIT', true);
define('DISALLOW_FILE_MODS', true);
define('FORCE_SSL_ADMIN', true);
define('WP_AUTO_UPDATE_CORE', true);

// Limit login attempts
define('WP_FAIL2BAN_BLOCKED_USERS', 'admin,administrator,root');

// Hide WordPress version
remove_action('wp_head', 'wp_generator');
```

#### Essential Security Plugins

1. **Wordfence Security** - Firewall and malware scanning
2. **iThemes Security** - Comprehensive security hardening
3. **WP Fail2Ban** - Integration with server-level blocking
4. **WP Security Audit Log** - Activity monitoring

### Performance Optimization Stack

#### Caching Strategy

1. **Server Level**: Nginx FastCGI caching
2. **Application Level**: Redis object cache
3. **CDN Level**: CloudFlare edge caching
4. **Plugin Level**: WP Rocket or W3 Total Cache

#### Nginx FastCGI Cache Configuration

```nginx
# In http block
fastcgi_cache_path /var/cache/nginx levels=1:2 keys_zone=WORDPRESS:100m inactive=60m;
fastcgi_cache_key "$scheme$request_method$host$request_uri";

# In server block
set $skip_cache 0;

# Don't cache for logged in users or recent commenters
if ($http_cookie ~* "comment_author|wordpress_[a-f0-9]+|wp-postpass|wordpress_no_cache|wordpress_logged_in") {
    set $skip_cache 1;
}

# Don't cache for admin pages
if ($request_uri ~* "/wp-admin/|/xmlrpc.php|wp-.*.php|/feed/|index.php|sitemap(_index)?.xml") {
    set $skip_cache 1;
}

location ~ \.php$ {
    fastcgi_cache WORDPRESS;
    fastcgi_cache_valid 200 60m;
    fastcgi_cache_bypass $skip_cache;
    fastcgi_no_cache $skip_cache;
    add_header X-Cache $upstream_cache_status;

    include snippets/fastcgi-php.conf;
    fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
}
```
